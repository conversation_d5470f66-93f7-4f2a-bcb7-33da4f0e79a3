"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, Heart, MessageCircle, Share2, Eye, Users } from "lucide-react"

type Business = {
  id: string
  businessName: string
}

type SocialMediaAccount = {
  id: string
  accountName: string
  platform: string
  status: string
}

type PostAnalytics = {
  id: string
  title: string
  platform: string
  publishedDate: string
  analytics: {
    likes: number
    comments: number
    shares: number
    impressions: number
    reach: number
  }
  media: Array<{ file: { url: string } }>
}

export default function SocialMediaAnalytics() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([])
  const [selectedBusiness, setSelectedBusiness] = useState<string>("")
  const [selectedAccount, setSelectedAccount] = useState<string>("")
  const [timeRange, setTimeRange] = useState<string>("7days")
  const [analytics, setAnalytics] = useState<PostAnalytics[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totals, setTotals] = useState({
    likes: 0,
    comments: 0,
    shares: 0,
    impressions: 0,
    reach: 0
  })

  useEffect(() => {
    // Fetch businesses
    const fetchBusinesses = async () => {
      try {
        const response = await fetch('/api/businesses')
        const data = await response.json()
        
        if (data.docs && data.docs.length > 0) {
          setBusinesses(data.docs)
          setSelectedBusiness(data.docs[0].id)
        }
      } catch (err) {
        console.error('Error fetching businesses:', err)
        setError('Failed to load businesses')
      }
    }

    fetchBusinesses()
  }, [])

  useEffect(() => {
    // Fetch social media accounts when a business is selected
    const fetchAccounts = async () => {
      if (!selectedBusiness) return
      
      try {
        const response = await fetch(`/api/social-media-accounts?business=${selectedBusiness}&status=connected`)
        const data = await response.json()
        
        if (data.docs && data.docs.length > 0) {
          setAccounts(data.docs)
          setSelectedAccount(data.docs[0].id)
        } else {
          setAccounts([])
          setSelectedAccount("")
        }
      } catch (err) {
        console.error('Error fetching social media accounts:', err)
        setError('Failed to load social media accounts')
      }
    }

    fetchAccounts()
  }, [selectedBusiness])

  useEffect(() => {
    // Fetch analytics when an account is selected
    const fetchAnalytics = async () => {
      if (!selectedAccount) {
        setAnalytics([])
        setIsLoading(false)
        return
      }
      
      setIsLoading(true)
      setError(null)
      
      try {
        // In a real app, you would include the time range in the query
        const response = await fetch(`/api/social-media-content?socialMediaAccount=${selectedAccount}&status=published`)
        const data = await response.json()
        
        if (data.docs) {
          setAnalytics(data.docs)
          
          // Calculate totals
          const totals = data.docs.reduce((acc: any, post: PostAnalytics) => {
            if (post.analytics) {
              acc.likes += post.analytics.likes || 0
              acc.comments += post.analytics.comments || 0
              acc.shares += post.analytics.shares || 0
              acc.impressions += post.analytics.impressions || 0
              acc.reach += post.analytics.reach || 0
            }
            return acc
          }, { likes: 0, comments: 0, shares: 0, impressions: 0, reach: 0 })
          
          setTotals(totals)
        } else {
          setAnalytics([])
          setTotals({ likes: 0, comments: 0, shares: 0, impressions: 0, reach: 0 })
        }
      } catch (err) {
        console.error('Error fetching analytics:', err)
        setError('Failed to load analytics')
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalytics()
  }, [selectedAccount, timeRange])

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="flex items-center space-x-2">
            <label htmlFor="business-select" className="text-sm font-medium">
              Business:
            </label>
            <Select value={selectedBusiness} onValueChange={setSelectedBusiness}>
              <SelectTrigger className="w-[200px]" id="business-select">
                <SelectValue placeholder="Select a business" />
              </SelectTrigger>
              <SelectContent>
                {businesses.map((business) => (
                  <SelectItem key={business.id} value={business.id}>
                    {business.businessName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-2">
            <label htmlFor="account-select" className="text-sm font-medium">
              Account:
            </label>
            <Select 
              value={selectedAccount} 
              onValueChange={setSelectedAccount}
              disabled={accounts.length === 0}
            >
              <SelectTrigger className="w-[200px]" id="account-select">
                <SelectValue placeholder={accounts.length === 0 ? "No accounts" : "Select an account"} />
              </SelectTrigger>
              <SelectContent>
                {accounts.map((account) => (
                  <SelectItem key={account.id} value={account.id}>
                    {account.accountName} ({account.platform})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <label htmlFor="time-range" className="text-sm font-medium">
            Time Range:
          </label>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[150px]" id="time-range">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="alltime">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 p-4 rounded-md flex items-center gap-2 text-red-800">
          <AlertCircle className="h-5 w-5" />
          <p>{error}</p>
        </div>
      )}

      {!selectedAccount ? (
        <div className="text-center py-10 border rounded-md bg-gray-50">
          <p className="text-gray-500">Please select an account to view analytics.</p>
        </div>
      ) : isLoading ? (
        <div className="text-center py-10">Loading analytics...</div>
      ) : (
        <>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Likes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Heart className="h-4 w-4 text-red-500 mr-2" />
                  <span className="text-2xl font-bold">{totals.likes.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Comments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <MessageCircle className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-2xl font-bold">{totals.comments.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Shares</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Share2 className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-2xl font-bold">{totals.shares.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Impressions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Eye className="h-4 w-4 text-purple-500 mr-2" />
                  <span className="text-2xl font-bold">{totals.impressions.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500">Reach</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-2xl font-bold">{totals.reach.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <h3 className="text-lg font-medium mt-8 mb-4">Post Performance</h3>
          
          {analytics.length === 0 ? (
            <div className="text-center py-10 border rounded-md bg-gray-50">
              <p className="text-gray-500">No published posts found for this account.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {analytics.map((post) => (
                <Card key={post.id}>
                  <div className="flex flex-col md:flex-row">
                    <div className="w-full md:w-1/4 p-4">
                      {post.media && post.media[0] && (
                        <img 
                          src={post.media[0].file.url} 
                          alt={post.title}
                          className="w-full h-32 object-cover rounded-md"
                        />
                      )}
                    </div>
                    <div className="w-full md:w-3/4 p-4">
                      <h4 className="font-medium">{post.title}</h4>
                      <p className="text-sm text-gray-500 mb-2">
                        Published: {formatDate(post.publishedDate)}
                      </p>
                      
                      <div className="grid grid-cols-5 gap-4 mt-4">
                        <div className="flex flex-col items-center">
                          <Heart className="h-4 w-4 text-red-500 mb-1" />
                          <span className="text-sm font-medium">{post.analytics?.likes || 0}</span>
                          <span className="text-xs text-gray-500">Likes</span>
                        </div>
                        
                        <div className="flex flex-col items-center">
                          <MessageCircle className="h-4 w-4 text-blue-500 mb-1" />
                          <span className="text-sm font-medium">{post.analytics?.comments || 0}</span>
                          <span className="text-xs text-gray-500">Comments</span>
                        </div>
                        
                        <div className="flex flex-col items-center">
                          <Share2 className="h-4 w-4 text-green-500 mb-1" />
                          <span className="text-sm font-medium">{post.analytics?.shares || 0}</span>
                          <span className="text-xs text-gray-500">Shares</span>
                        </div>
                        
                        <div className="flex flex-col items-center">
                          <Eye className="h-4 w-4 text-purple-500 mb-1" />
                          <span className="text-sm font-medium">{post.analytics?.impressions || 0}</span>
                          <span className="text-xs text-gray-500">Impressions</span>
                        </div>
                        
                        <div className="flex flex-col items-center">
                          <Users className="h-4 w-4 text-orange-500 mb-1" />
                          <span className="text-sm font-medium">{post.analytics?.reach || 0}</span>
                          <span className="text-xs text-gray-500">Reach</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  )
}
