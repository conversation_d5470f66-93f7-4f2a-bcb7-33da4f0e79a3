'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Instagram,
  Image,
  FileVideo,
  LayoutGrid,
  AlertCircle,
  Calendar,
  Upload,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

type Business = {
  id: string
  businessName: string
}

export default function SocialMediaContent() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [selectedBusiness, setSelectedBusiness] = useState<string>('')
  const [selectedPlatform, setSelectedPlatform] = useState<string>('instagram')
  const [contentType, setContentType] = useState<string>('image')
  const [title, setTitle] = useState<string>('')
  const [caption, setCaption] = useState<string>('')
  const [hashtags, setHashtags] = useState<string>('')
  const [visualDescription, setVisualDescription] = useState<string>('')
  const [files, setFiles] = useState<File[]>([])
  const [previewUrls, setPreviewUrls] = useState<string[]>([])
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [scheduledDate, setScheduledDate] = useState<string>('')
  const [scheduledTime, setScheduledTime] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [characterCount, setCharacterCount] = useState(0)

  useEffect(() => {
    // Fetch businesses
    const fetchBusinesses = async () => {
      try {
        const response = await fetch('/api/businesses')
        const data = await response.json()

        if (data.docs && data.docs.length > 0) {
          setBusinesses(data.docs)
          setSelectedBusiness(data.docs[0].id)
        }
      } catch (err) {
        console.error('Error fetching businesses:', err)
        setError('Failed to load businesses')
      }
    }

    fetchBusinesses()
  }, [])

  useEffect(() => {
    // Update character count for Instagram caption
    if (selectedPlatform === 'instagram') {
      setCharacterCount(caption.length)
    }
  }, [caption, selectedPlatform])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files)
      setFiles(selectedFiles)

      // Create preview URLs
      const urls = selectedFiles.map((file) => URL.createObjectURL(file))
      setPreviewUrls(urls)
    }
  }

  const handleSchedulePost = () => {
    setIsScheduleDialogOpen(false)
    submitPost(true)
  }

  const submitPost = async (isScheduled: boolean) => {
    if (!selectedBusiness || !title || !caption || files.length === 0) {
      setError('Please fill in all required fields and upload at least one media file')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // First upload the media files
      const mediaIds = await Promise.all(
        files.map(async (file) => {
          const formData = new FormData()
          formData.append('file', file)

          const uploadResponse = await fetch('/api/media', {
            method: 'POST',
            body: formData,
          })

          const uploadData = await uploadResponse.json()

          // Check if the response has the expected structure
          if (uploadData && uploadData.id) {
            return uploadData.id
          } else if (uploadData && uploadData.doc && uploadData.doc.id) {
            return uploadData.doc.id
          } else {
            console.error('Unexpected media upload response format:', uploadData)
            throw new Error('Invalid media upload response format')
          }
        }),
      )

      // Parse hashtags
      const hashtagArray = hashtags
        .split(' ')
        .filter((tag) => tag.trim() !== '')
        .map((tag) => ({ tag: tag.startsWith('#') ? tag : `#${tag}` }))

      // Create the post data
      const postData = {
        title,
        business: selectedBusiness,
        platform: selectedPlatform,
        contentType,
        status: isScheduled ? 'scheduled' : 'draft',
        caption,
        hashtags: hashtagArray,
        visualDescription,
        media: mediaIds.map((id) => ({ file: id })),
        scheduledDate: isScheduled
          ? new Date(`${scheduledDate}T${scheduledTime}`).toISOString()
          : null,
        aiGenerated: false,
      }

      // Submit the post
      const response = await fetch('/api/social-media-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      })

      if (!response.ok) {
        throw new Error('Failed to create post')
      }

      // Reset form
      setTitle('')
      setCaption('')
      setHashtags('')
      setVisualDescription('')
      setFiles([])
      setPreviewUrls([])
      setScheduledDate('')
      setScheduledTime('')

      // Show success message or redirect
      alert(isScheduled ? 'Post scheduled successfully!' : 'Post saved as draft!')
    } catch (err) {
      console.error('Error creating post:', err)
      setError('Failed to create post. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 p-4 rounded-md flex items-center gap-2 text-red-800">
          <AlertCircle className="h-5 w-5" />
          <p>{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="business">Business</Label>
            <Select value={selectedBusiness} onValueChange={setSelectedBusiness}>
              <SelectTrigger id="business">
                <SelectValue placeholder="Select a business" />
              </SelectTrigger>
              <SelectContent>
                {businesses.map((business) => (
                  <SelectItem key={business.id} value={business.id}>
                    {business.businessName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="platform">Platform</Label>
            <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
              <SelectTrigger id="platform">
                <SelectValue placeholder="Select a platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="instagram">
                  <div className="flex items-center gap-2">
                    <Instagram className="h-4 w-4" />
                    <span>Instagram</span>
                  </div>
                </SelectItem>
                <SelectItem value="facebook" disabled>
                  <div className="flex items-center gap-2 opacity-50">
                    <span>Facebook (Coming Soon)</span>
                  </div>
                </SelectItem>
                <SelectItem value="twitter" disabled>
                  <div className="flex items-center gap-2 opacity-50">
                    <span>Twitter (Coming Soon)</span>
                  </div>
                </SelectItem>
                <SelectItem value="linkedin" disabled>
                  <div className="flex items-center gap-2 opacity-50">
                    <span>LinkedIn (Coming Soon)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="content-type">Content Type</Label>
            <Select value={contentType} onValueChange={setContentType}>
              <SelectTrigger id="content-type">
                <SelectValue placeholder="Select content type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="image">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    <span>Single Image</span>
                  </div>
                </SelectItem>
                <SelectItem value="carousel">
                  <div className="flex items-center gap-2">
                    <LayoutGrid className="h-4 w-4" />
                    <span>Carousel</span>
                  </div>
                </SelectItem>
                <SelectItem value="video">
                  <div className="flex items-center gap-2">
                    <FileVideo className="h-4 w-4" />
                    <span>Video</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="title">Post Title (for internal reference)</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter a title for this post"
            />
          </div>

          <div>
            <Label htmlFor="caption">Caption</Label>
            <Textarea
              id="caption"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              placeholder="Write your caption here..."
              className="min-h-[120px]"
            />
            {selectedPlatform === 'instagram' && (
              <p className="text-sm text-gray-500 mt-1">{characterCount}/2200 characters</p>
            )}
          </div>

          <div>
            <Label htmlFor="hashtags">Hashtags</Label>
            <Textarea
              id="hashtags"
              value={hashtags}
              onChange={(e) => setHashtags(e.target.value)}
              placeholder="Enter hashtags separated by spaces (e.g. #dukanify #business)"
            />
            {selectedPlatform === 'instagram' && (
              <p className="text-sm text-gray-500 mt-1">
                Instagram allows up to 30 hashtags per post
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="visual-description">Visual Description</Label>
            <Textarea
              id="visual-description"
              value={visualDescription}
              onChange={(e) => setVisualDescription(e.target.value)}
              placeholder="Describe the visual elements, composition, colors, and overall aesthetic of the content..."
              className="min-h-[100px]"
            />
            <p className="text-sm text-gray-500 mt-1">
              Describe the visual elements to help with content planning and AI generation
            </p>
          </div>

          <div>
            <Label htmlFor="media">Upload Media</Label>
            <div className="mt-1 flex items-center">
              <label className="block w-full">
                <span className="sr-only">Choose files</span>
                <Input
                  id="media"
                  type="file"
                  multiple={contentType === 'carousel'}
                  accept="image/*,video/*"
                  onChange={handleFileChange}
                  className="block w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-semibold
                    file:bg-primary file:text-primary-foreground
                    hover:file:bg-primary/90"
                />
              </label>
            </div>
            {selectedPlatform === 'instagram' && contentType === 'carousel' && (
              <p className="text-sm text-gray-500 mt-1">
                Instagram carousel: 2-10 images with the same aspect ratio
              </p>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Preview</h3>

          {previewUrls.length > 0 ? (
            <div
              className={`border rounded-md p-4 ${contentType === 'carousel' ? 'grid grid-cols-2 gap-2' : ''}`}
            >
              {previewUrls.map((url, index) => (
                <div key={index} className="relative">
                  <img
                    src={url}
                    alt={`Preview ${index + 1}`}
                    className="rounded-md max-h-[300px] w-full object-cover"
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="border rounded-md p-10 flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">Upload media to see preview</p>
              </div>
            </div>
          )}

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 rounded-full bg-gray-200"></div>
                <div>
                  <p className="text-sm font-medium">
                    {businesses.find((b) => b.id === selectedBusiness)?.businessName ||
                      'Business Name'}
                  </p>
                </div>
              </div>

              <p className="text-sm whitespace-pre-line mb-2">{caption}</p>

              {hashtags && (
                <p className="text-sm text-blue-600">
                  {hashtags.split(' ').map((tag, i) => (
                    <span key={i}>{tag.startsWith('#') ? tag : `#${tag}`} </span>
                  ))}
                </p>
              )}
            </CardContent>
          </Card>

          <div className="flex gap-4 mt-6">
            <Button
              onClick={() => submitPost(false)}
              variant="outline"
              disabled={isSubmitting || !selectedBusiness}
              className="flex-1"
            >
              Save as Draft
            </Button>

            <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
              <DialogTrigger asChild>
                <Button disabled={isSubmitting || !selectedBusiness} className="flex-1">
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule Post
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Schedule Your Post</DialogTitle>
                  <DialogDescription>
                    Choose when you want your post to be published.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div>
                    <Label htmlFor="schedule-date">Date</Label>
                    <Input
                      id="schedule-date"
                      type="date"
                      value={scheduledDate}
                      onChange={(e) => setScheduledDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                  <div>
                    <Label htmlFor="schedule-time">Time</Label>
                    <Input
                      id="schedule-time"
                      type="time"
                      value={scheduledTime}
                      onChange={(e) => setScheduledTime(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSchedulePost} disabled={!scheduledDate || !scheduledTime}>
                    Schedule
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  )
}
