/**
 * Social Media Post Scheduler Service
 * 
 * This service handles scheduling and processing of social media posts.
 */

import payload from 'payload';
import { postImageToInstagram, postCarouselToInstagram } from './instagram';

// Maximum number of retry attempts for failed posts
const MAX_RETRY_ATTEMPTS = 3;

/**
 * Process scheduled posts that are due for publishing
 */
export const processScheduledPosts = async () => {
  const now = new Date();
  
  try {
    // Find all scheduled posts that are due
    const scheduledPosts = await payload.find({
      collection: 'social-media-content',
      where: {
        and: [
          {
            status: {
              equals: 'scheduled',
            },
          },
          {
            scheduledDate: {
              less_than_equal: now.toISOString(),
            },
          },
        ],
      },
      depth: 2, // Load relationships
    });

    console.log(`Found ${scheduledPosts.totalDocs} scheduled posts to process`);

    // Process each post
    for (const post of scheduledPosts.docs) {
      try {
        await processPost(post);
      } catch (error) {
        console.error(`Error processing post ${post.id}:`, error);
        
        // Update post with error information
        const attempts = (post.postingDetails?.attempts || 0) + 1;
        const updateData: any = {
          'postingDetails.attempts': attempts,
          'postingDetails.lastAttemptAt': new Date().toISOString(),
          'postingDetails.error': error.message || 'Unknown error occurred',
        };
        
        // Mark as failed if max attempts reached
        if (attempts >= MAX_RETRY_ATTEMPTS) {
          updateData.status = 'failed';
        }
        
        await payload.update({
          collection: 'social-media-content',
          id: post.id,
          data: updateData,
        });
      }
    }
  } catch (error) {
    console.error('Error processing scheduled posts:', error);
  }
};

/**
 * Process a single social media post
 * @param post The post to process
 */
const processPost = async (post: any) => {
  // Get the social media account
  const socialMediaAccount = await payload.findByID({
    collection: 'social-media-accounts',
    id: post.socialMediaAccount,
  });

  if (!socialMediaAccount) {
    throw new Error('Social media account not found');
  }

  if (socialMediaAccount.status !== 'connected') {
    throw new Error('Social media account is not connected');
  }

  // Get access token
  const accessToken = socialMediaAccount.authDetails?.accessToken;
  if (!accessToken) {
    throw new Error('Access token not found for social media account');
  }

  // Process based on platform
  switch (post.platform) {
    case 'instagram':
      await processInstagramPost(post, socialMediaAccount);
      break;
    // Add cases for other platforms here
    default:
      throw new Error(`Unsupported platform: ${post.platform}`);
  }

  // Update post status to published
  await payload.update({
    collection: 'social-media-content',
    id: post.id,
    data: {
      status: 'published',
      publishedDate: new Date().toISOString(),
    },
  });
};

/**
 * Process an Instagram post
 * @param post The post to process
 * @param account The Instagram account
 */
const processInstagramPost = async (post: any, account: any) => {
  // Get Instagram account ID and access token
  const instagramAccountId = account.authDetails.userId;
  const accessToken = account.authDetails.accessToken;

  // Get media files
  const mediaFiles = post.media || [];
  if (mediaFiles.length === 0) {
    throw new Error('No media files found for Instagram post');
  }

  // Get media URLs
  const mediaUrls = await Promise.all(
    mediaFiles.map(async (media: any) => {
      const mediaDoc = await payload.findByID({
        collection: 'media',
        id: media.file,
      });
      
      // Use the full URL of the media file
      return `${process.env.PAYLOAD_PUBLIC_SERVER_URL}${mediaDoc.url}`;
    })
  );

  // Prepare hashtags
  const hashtags = post.hashtags ? post.hashtags.map((tag: any) => tag.tag) : [];

  // Prepare Instagram-specific options
  const instagramOptions = {
    hashtags,
    firstComment: post.instagramSpecific?.firstComment,
    locationName: post.instagramSpecific?.locationName,
    userTags: post.instagramSpecific?.userTags,
  };

  // Post to Instagram based on content type
  let result;
  switch (post.contentType) {
    case 'image':
      result = await postImageToInstagram(
        instagramAccountId,
        accessToken,
        mediaUrls[0],
        post.caption,
        instagramOptions
      );
      break;
    case 'carousel':
      result = await postCarouselToInstagram(
        instagramAccountId,
        accessToken,
        mediaUrls,
        post.caption,
        instagramOptions
      );
      break;
    // Add cases for other content types here
    default:
      throw new Error(`Unsupported content type for Instagram: ${post.contentType}`);
  }

  // Update post with result information
  await payload.update({
    collection: 'social-media-content',
    id: post.id,
    data: {
      'postingDetails.postId': result.id,
      'postingDetails.postUrl': `https://www.instagram.com/p/${result.id}`,
      'postingDetails.lastAttemptAt': new Date().toISOString(),
    },
  });
};

/**
 * Start the scheduler to run at regular intervals
 * @param intervalMinutes Interval in minutes between scheduler runs
 */
export const startScheduler = (intervalMinutes = 5) => {
  // Run immediately on startup
  processScheduledPosts();
  
  // Then run at regular intervals
  const intervalMs = intervalMinutes * 60 * 1000;
  setInterval(processScheduledPosts, intervalMs);
  
  console.log(`Social media post scheduler started with ${intervalMinutes} minute interval`);
};
