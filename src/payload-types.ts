/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    businesses: Business;
    websites: Website;
    'social-media-accounts': SocialMediaAccount;
    'social-media-content': SocialMediaContent;
    invoices: Invoice;
    'marketing-campaigns': MarketingCampaign;
    'youtube-videos': YoutubeVideo;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    businesses: BusinessesSelect<false> | BusinessesSelect<true>;
    websites: WebsitesSelect<false> | WebsitesSelect<true>;
    'social-media-accounts': SocialMediaAccountsSelect<false> | SocialMediaAccountsSelect<true>;
    'social-media-content': SocialMediaContentSelect<false> | SocialMediaContentSelect<true>;
    invoices: InvoicesSelect<false> | InvoicesSelect<true>;
    'marketing-campaigns': MarketingCampaignsSelect<false> | MarketingCampaignsSelect<true>;
    'youtube-videos': YoutubeVideosSelect<false> | YoutubeVideosSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  name?: string | null;
  role: 'admin' | 'user';
  phone?: string | null;
  profileImage?: (string | null) | Media;
  subscription?: {
    plan?: ('free' | 'basic' | 'premium' | 'enterprise') | null;
    status?: ('active' | 'inactive' | 'trial' | 'expired') | null;
    startDate?: string | null;
    endDate?: string | null;
    razorpayCustomerId?: string | null;
  };
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "businesses".
 */
export interface Business {
  id: string;
  businessName: string;
  logo?: (string | null) | Media;
  /**
   * Describe your brand, values, and target audience (max 1000 characters)
   */
  brandBrief?: string | null;
  industry:
    | 'retail'
    | 'food_beverage'
    | 'technology'
    | 'healthcare'
    | 'education'
    | 'professional_services'
    | 'manufacturing'
    | 'construction'
    | 'transportation'
    | 'hospitality'
    | 'other';
  location?: {
    address?: string | null;
    city?: string | null;
    state?: string | null;
    pincode?: string | null;
  };
  contactInfo?: {
    email?: string | null;
    phone?: string | null;
    website?: string | null;
  };
  brandElements?: {
    colorPalette?:
      | {
          colorHex?: string | null;
          colorName?: string | null;
          id?: string | null;
        }[]
      | null;
    seoKeywords?:
      | {
          keyword?: string | null;
          relevanceScore?: number | null;
          id?: string | null;
        }[]
      | null;
    competitors?:
      | {
          name?: string | null;
          website?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  owner?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "websites".
 */
export interface Website {
  id: string;
  name: string;
  business: string | Business;
  status: 'draft' | 'published' | 'archived';
  template: 'ecommerce' | 'business' | 'portfolio' | 'blog' | 'restaurant' | 'service';
  domain?: {
    customDomain?: string | null;
    /**
     * Your site will be available at [subdomain].dukanify.com
     */
    subdomain?: string | null;
    isConnected?: boolean | null;
  };
  pages?:
    | {
        title: string;
        slug: string;
        layout?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        seo?: {
          title?: string | null;
          description?: string | null;
          keywords?: string | null;
        };
        id?: string | null;
      }[]
    | null;
  products?:
    | {
        name: string;
        description?: string | null;
        price: number;
        images?:
          | {
              image: string | Media;
              id?: string | null;
            }[]
          | null;
        id?: string | null;
      }[]
    | null;
  analytics?: {
    googleAnalyticsId?: string | null;
    facebookPixelId?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "social-media-accounts".
 */
export interface SocialMediaAccount {
  id: string;
  accountName: string;
  business: string | Business;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube';
  status: 'connected' | 'disconnected' | 'error';
  username?: string | null;
  profileUrl?: string | null;
  profilePicture?: (string | null) | Media;
  authDetails?: {
    accessToken?: string | null;
    refreshToken?: string | null;
    tokenExpiry?: string | null;
    userId?: string | null;
  };
  instagramSpecific?: {
    businessAccount?: boolean | null;
    mediaCount?: number | null;
    followerCount?: number | null;
    followingCount?: number | null;
  };
  lastSyncedAt?: string | null;
  lastSyncStatus?: ('success' | 'failed' | 'never') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "social-media-content".
 */
export interface SocialMediaContent {
  id: string;
  title: string;
  business: string | Business;
  /**
   * Select the social media account to post from
   */
  socialMediaAccount?: (string | null) | SocialMediaAccount;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube';
  contentType: 'image' | 'carousel' | 'video' | 'story' | 'text';
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  caption?: string | null;
  hashtags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  media?:
    | {
        file: string | Media;
        altText?: string | null;
        mediaValidation?: {
          width?: number | null;
          height?: number | null;
          aspectRatio?: number | null;
          duration?: number | null;
        };
        id?: string | null;
      }[]
    | null;
  scheduledDate?: string | null;
  publishedDate?: string | null;
  analytics?: {
    likes?: number | null;
    comments?: number | null;
    shares?: number | null;
    impressions?: number | null;
    reach?: number | null;
  };
  aiGenerated?: boolean | null;
  generationPrompt?: string | null;
  postingDetails?: {
    postId?: string | null;
    postUrl?: string | null;
    attempts?: number | null;
    lastAttemptAt?: string | null;
    error?: string | null;
  };
  instagramSpecific?: {
    /**
     * Add a comment that will be posted immediately after publishing
     */
    firstComment?: string | null;
    locationName?: string | null;
    userTags?:
      | {
          username: string;
          x?: number | null;
          y?: number | null;
          id?: string | null;
        }[]
      | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "invoices".
 */
export interface Invoice {
  id: string;
  invoiceNumber: string;
  business: string | Business;
  customer: {
    name: string;
    email?: string | null;
    phone?: string | null;
    address?: string | null;
    gstin?: string | null;
  };
  invoiceDate: string;
  dueDate?: string | null;
  items: {
    description: string;
    quantity: number;
    unitPrice: number;
    hsnSac?: string | null;
    taxRate: '0' | '5' | '12' | '18' | '28';
    taxAmount?: number | null;
    totalAmount?: number | null;
    id?: string | null;
  }[];
  subtotal?: number | null;
  taxTotal?: number | null;
  totalAmount?: number | null;
  notes?: string | null;
  termsAndConditions?: string | null;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  paymentDetails?: {
    method?: ('cash' | 'bank_transfer' | 'upi' | 'credit_card' | 'debit_card' | 'cheque') | null;
    transactionId?: string | null;
    paidDate?: string | null;
    paidAmount?: number | null;
  };
  aiGenerated?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketing-campaigns".
 */
export interface MarketingCampaign {
  id: string;
  name: string;
  business: string | Business;
  type: 'seo' | 'social_media' | 'email' | 'google_ads' | 'facebook_ads' | 'content_marketing' | 'local_seo';
  description?: string | null;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate?: string | null;
  endDate?: string | null;
  budget?: number | null;
  targetAudience?: {
    demographics?: {
      ageRange?: ('all' | '18-24' | '25-34' | '35-44' | '45-54' | '55+') | null;
      gender?: ('all' | 'male' | 'female') | null;
      locations?:
        | {
            location?: string | null;
            id?: string | null;
          }[]
        | null;
    };
    interests?:
      | {
          interest?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  keywords?:
    | {
        keyword: string;
        difficulty?: ('easy' | 'medium' | 'hard') | null;
        searchVolume?: number | null;
        id?: string | null;
      }[]
    | null;
  googleBusinessProfile?: {
    isConnected?: boolean | null;
    profileUrl?: string | null;
    lastUpdated?: string | null;
  };
  content?:
    | {
        title: string;
        description?: string | null;
        contentType: 'blog' | 'social' | 'ad' | 'email' | 'landing_page';
        status?: ('draft' | 'published' | 'scheduled') | null;
        media?: (string | null) | Media;
        id?: string | null;
      }[]
    | null;
  analytics?: {
    impressions?: number | null;
    clicks?: number | null;
    conversions?: number | null;
    spend?: number | null;
    roi?: number | null;
  };
  aiGenerated?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "youtube-videos".
 */
export interface YoutubeVideo {
  id: string;
  title: string;
  description?: string | null;
  business: string | Business;
  /**
   * Select the YouTube account to use
   */
  youtubeAccount: string | SocialMediaAccount;
  /**
   * The unique identifier for the video on YouTube
   */
  videoId?: string | null;
  /**
   * Full URL to the video on YouTube
   */
  videoUrl?: string | null;
  thumbnailUrl?: string | null;
  /**
   * Upload a custom thumbnail for this video
   */
  customThumbnail?: (string | null) | Media;
  /**
   * Upload the video file (for videos to be uploaded to YouTube)
   */
  videoFile?: (string | null) | Media;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  visibility: 'public' | 'unlisted' | 'private';
  /**
   * When the video was or will be published on YouTube
   */
  publishedDate?: string | null;
  /**
   * When to publish this video to YouTube
   */
  scheduledDate?: string | null;
  category?:
    | (
        | 'film_animation'
        | 'autos_vehicles'
        | 'music'
        | 'pets_animals'
        | 'sports'
        | 'travel_events'
        | 'gaming'
        | 'people_blogs'
        | 'comedy'
        | 'entertainment'
        | 'news_politics'
        | 'howto_style'
        | 'education'
        | 'science_technology'
        | 'nonprofits_activism'
      )
    | null;
  /**
   * Tags to help viewers find your video
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Statistics from YouTube (automatically updated)
   */
  statistics?: {
    views?: number | null;
    likes?: number | null;
    comments?: number | null;
    lastUpdated?: string | null;
  };
  /**
   * Information about the upload process
   */
  uploadStatus?: {
    progress?: number | null;
    message?: string | null;
    error?: string | null;
    retryCount?: number | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'businesses';
        value: string | Business;
      } | null)
    | ({
        relationTo: 'websites';
        value: string | Website;
      } | null)
    | ({
        relationTo: 'social-media-accounts';
        value: string | SocialMediaAccount;
      } | null)
    | ({
        relationTo: 'social-media-content';
        value: string | SocialMediaContent;
      } | null)
    | ({
        relationTo: 'invoices';
        value: string | Invoice;
      } | null)
    | ({
        relationTo: 'marketing-campaigns';
        value: string | MarketingCampaign;
      } | null)
    | ({
        relationTo: 'youtube-videos';
        value: string | YoutubeVideo;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  role?: T;
  phone?: T;
  profileImage?: T;
  subscription?:
    | T
    | {
        plan?: T;
        status?: T;
        startDate?: T;
        endDate?: T;
        razorpayCustomerId?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "businesses_select".
 */
export interface BusinessesSelect<T extends boolean = true> {
  businessName?: T;
  logo?: T;
  brandBrief?: T;
  industry?: T;
  location?:
    | T
    | {
        address?: T;
        city?: T;
        state?: T;
        pincode?: T;
      };
  contactInfo?:
    | T
    | {
        email?: T;
        phone?: T;
        website?: T;
      };
  brandElements?:
    | T
    | {
        colorPalette?:
          | T
          | {
              colorHex?: T;
              colorName?: T;
              id?: T;
            };
        seoKeywords?:
          | T
          | {
              keyword?: T;
              relevanceScore?: T;
              id?: T;
            };
        competitors?:
          | T
          | {
              name?: T;
              website?: T;
              id?: T;
            };
      };
  owner?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "websites_select".
 */
export interface WebsitesSelect<T extends boolean = true> {
  name?: T;
  business?: T;
  status?: T;
  template?: T;
  domain?:
    | T
    | {
        customDomain?: T;
        subdomain?: T;
        isConnected?: T;
      };
  pages?:
    | T
    | {
        title?: T;
        slug?: T;
        layout?: T;
        seo?:
          | T
          | {
              title?: T;
              description?: T;
              keywords?: T;
            };
        id?: T;
      };
  products?:
    | T
    | {
        name?: T;
        description?: T;
        price?: T;
        images?:
          | T
          | {
              image?: T;
              id?: T;
            };
        id?: T;
      };
  analytics?:
    | T
    | {
        googleAnalyticsId?: T;
        facebookPixelId?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "social-media-accounts_select".
 */
export interface SocialMediaAccountsSelect<T extends boolean = true> {
  accountName?: T;
  business?: T;
  platform?: T;
  status?: T;
  username?: T;
  profileUrl?: T;
  profilePicture?: T;
  authDetails?:
    | T
    | {
        accessToken?: T;
        refreshToken?: T;
        tokenExpiry?: T;
        userId?: T;
      };
  instagramSpecific?:
    | T
    | {
        businessAccount?: T;
        mediaCount?: T;
        followerCount?: T;
        followingCount?: T;
      };
  lastSyncedAt?: T;
  lastSyncStatus?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "social-media-content_select".
 */
export interface SocialMediaContentSelect<T extends boolean = true> {
  title?: T;
  business?: T;
  socialMediaAccount?: T;
  platform?: T;
  contentType?: T;
  status?: T;
  caption?: T;
  hashtags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  media?:
    | T
    | {
        file?: T;
        altText?: T;
        mediaValidation?:
          | T
          | {
              width?: T;
              height?: T;
              aspectRatio?: T;
              duration?: T;
            };
        id?: T;
      };
  scheduledDate?: T;
  publishedDate?: T;
  analytics?:
    | T
    | {
        likes?: T;
        comments?: T;
        shares?: T;
        impressions?: T;
        reach?: T;
      };
  aiGenerated?: T;
  generationPrompt?: T;
  postingDetails?:
    | T
    | {
        postId?: T;
        postUrl?: T;
        attempts?: T;
        lastAttemptAt?: T;
        error?: T;
      };
  instagramSpecific?:
    | T
    | {
        firstComment?: T;
        locationName?: T;
        userTags?:
          | T
          | {
              username?: T;
              x?: T;
              y?: T;
              id?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "invoices_select".
 */
export interface InvoicesSelect<T extends boolean = true> {
  invoiceNumber?: T;
  business?: T;
  customer?:
    | T
    | {
        name?: T;
        email?: T;
        phone?: T;
        address?: T;
        gstin?: T;
      };
  invoiceDate?: T;
  dueDate?: T;
  items?:
    | T
    | {
        description?: T;
        quantity?: T;
        unitPrice?: T;
        hsnSac?: T;
        taxRate?: T;
        taxAmount?: T;
        totalAmount?: T;
        id?: T;
      };
  subtotal?: T;
  taxTotal?: T;
  totalAmount?: T;
  notes?: T;
  termsAndConditions?: T;
  status?: T;
  paymentDetails?:
    | T
    | {
        method?: T;
        transactionId?: T;
        paidDate?: T;
        paidAmount?: T;
      };
  aiGenerated?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketing-campaigns_select".
 */
export interface MarketingCampaignsSelect<T extends boolean = true> {
  name?: T;
  business?: T;
  type?: T;
  description?: T;
  status?: T;
  startDate?: T;
  endDate?: T;
  budget?: T;
  targetAudience?:
    | T
    | {
        demographics?:
          | T
          | {
              ageRange?: T;
              gender?: T;
              locations?:
                | T
                | {
                    location?: T;
                    id?: T;
                  };
            };
        interests?:
          | T
          | {
              interest?: T;
              id?: T;
            };
      };
  keywords?:
    | T
    | {
        keyword?: T;
        difficulty?: T;
        searchVolume?: T;
        id?: T;
      };
  googleBusinessProfile?:
    | T
    | {
        isConnected?: T;
        profileUrl?: T;
        lastUpdated?: T;
      };
  content?:
    | T
    | {
        title?: T;
        description?: T;
        contentType?: T;
        status?: T;
        media?: T;
        id?: T;
      };
  analytics?:
    | T
    | {
        impressions?: T;
        clicks?: T;
        conversions?: T;
        spend?: T;
        roi?: T;
      };
  aiGenerated?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "youtube-videos_select".
 */
export interface YoutubeVideosSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  business?: T;
  youtubeAccount?: T;
  videoId?: T;
  videoUrl?: T;
  thumbnailUrl?: T;
  customThumbnail?: T;
  videoFile?: T;
  status?: T;
  visibility?: T;
  publishedDate?: T;
  scheduledDate?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  statistics?:
    | T
    | {
        views?: T;
        likes?: T;
        comments?: T;
        lastUpdated?: T;
      };
  uploadStatus?:
    | T
    | {
        progress?: T;
        message?: T;
        error?: T;
        retryCount?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}