import { NextRequest, NextResponse } from 'next/server'
import payload from 'payload'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')
    const business = searchParams.get('business')
    const platform = searchParams.get('platform')
    const status = searchParams.get('status')
    
    if (id) {
      // Get a specific social media account
      const account = await payload.findByID({
        collection: 'social-media-accounts',
        id,
        depth: 1,
      })
      
      return NextResponse.json(account)
    } else {
      // Build query based on parameters
      const query: any = {}
      
      if (business) {
        query.business = {
          equals: business,
        }
      }
      
      if (platform) {
        query.platform = {
          equals: platform,
        }
      }
      
      if (status) {
        query.status = {
          equals: status,
        }
      }
      
      // Get social media accounts based on query
      const accounts = await payload.find({
        collection: 'social-media-accounts',
        where: Object.keys(query).length > 0 ? { and: [query] } : {},
        depth: 1,
      })
      
      return NextResponse.json(accounts)
    }
  } catch (error: any) {
    console.error('Error fetching social media accounts:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Create a new social media account
    const account = await payload.create({
      collection: 'social-media-accounts',
      data: body,
    })
    
    return NextResponse.json(account)
  } catch (error: any) {
    console.error('Error creating social media account:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}
