import { NextRequest, NextResponse } from 'next/server'
import payload from 'payload'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')
    const business = searchParams.get('business')
    const platform = searchParams.get('platform')
    const status = searchParams.get('status')
    const socialMediaAccount = searchParams.get('socialMediaAccount')
    
    if (id) {
      // Get a specific social media content
      const content = await payload.findByID({
        collection: 'social-media-content',
        id,
        depth: 2,
      })
      
      return NextResponse.json(content)
    } else {
      // Build query based on parameters
      const query: any = {}
      
      if (business) {
        query.business = {
          equals: business,
        }
      }
      
      if (platform) {
        query.platform = {
          equals: platform,
        }
      }
      
      if (status) {
        query.status = {
          equals: status,
        }
      }
      
      if (socialMediaAccount) {
        query.socialMediaAccount = {
          equals: socialMediaAccount,
        }
      }
      
      // Get social media content based on query
      const content = await payload.find({
        collection: 'social-media-content',
        where: Object.keys(query).length > 0 ? { and: [query] } : {},
        depth: 2,
      })
      
      return NextResponse.json(content)
    }
  } catch (error: any) {
    console.error('Error fetching social media content:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Create a new social media content
    const content = await payload.create({
      collection: 'social-media-content',
      data: body,
    })
    
    return NextResponse.json(content)
  } catch (error: any) {
    console.error('Error creating social media content:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}
