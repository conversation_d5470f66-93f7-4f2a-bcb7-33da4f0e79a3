import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    if (id) {
      // Get a specific business
      const business = await payload.findByID({
        collection: 'businesses',
        id,
      })

      return NextResponse.json(business)
    } else {
      // Get all businesses for the current user
      const businesses = await payload.find({
        collection: 'businesses',
        depth: 0,
      })

      return NextResponse.json(businesses)
    }
  } catch (error: any) {
    console.error('Error fetching businesses:', error)
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Log the request headers for debugging
    console.log('Request headers:', Object.fromEntries(request.headers.entries()))

    // Check content type to determine how to parse the request
    const contentType = request.headers.get('content-type') || ''

    let data

    if (contentType.includes('multipart/form-data')) {
      // Handle form data
      const formData = await request.formData()

      // Log the form data for debugging
      console.log('Form data keys:', [...formData.keys()])

      // Check if this is Payload CMS admin interface data
      const payloadData = formData.get('_payload')
      if (payloadData) {
        try {
          // Parse the JSON data from Payload CMS admin interface
          data = JSON.parse(payloadData as string)
          console.log('Parsed Payload CMS data:', JSON.stringify(data, null, 2))
          console.log('Business Name from parsed data:', data.businessName)
          console.log('Industry from parsed data:', data.industry)
        } catch (parseError) {
          console.error('Error parsing _payload field:', parseError)
          return NextResponse.json({ error: 'Invalid JSON in _payload field' }, { status: 400 })
        }
      } else {
        // Convert form data to object (for custom form submissions)
        data = {}
        for (const [key, value] of formData.entries()) {
          // Handle file uploads
          if (value instanceof File) {
            console.log(`File found in key ${key}:`, value.name, value.type, value.size)
            // Skip file handling in this endpoint - redirect to create-with-logo endpoint
            return NextResponse.json(
              { error: 'File uploads should use the /api/businesses/create-with-logo endpoint' },
              { status: 400 },
            )
          }

          // Handle nested objects with dot notation (e.g., location.city)
          if (key.includes('.')) {
            const [parent, child] = key.split('.')
            if (!data[parent]) data[parent] = {}
            data[parent][child] = value
          } else {
            data[key] = value
          }
        }
      }
    } else {
      // Handle JSON data
      try {
        data = await request.json()
      } catch (jsonError) {
        console.error('Error parsing JSON:', jsonError)
        return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 })
      }
    }

    // Check for required fields
    console.log('Validating required fields...')
    console.log('businessName:', data.businessName)
    console.log('industry:', data.industry)

    if (!data.businessName) {
      console.log('ERROR: Business Name is missing')
      return NextResponse.json({ error: 'Business Name is required' }, { status: 400 })
    }

    if (!data.industry) {
      console.log('ERROR: Industry is missing')
      return NextResponse.json({ error: 'Industry is required' }, { status: 400 })
    }

    console.log('Required fields validation passed')

    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    // Get the current user
    let userId = null
    try {
      const { user } = await payload.findGlobal({
        slug: 'current-user',
        depth: 0,
      })

      if (user && user.id) {
        userId = user.id
        console.log('Found current user:', userId)
      }
    } catch (userError) {
      console.error('Error getting current user:', userError)
    }

    // If we couldn't get the current user, try to find any user
    if (!userId) {
      try {
        const users = await payload.find({
          collection: 'users',
          limit: 1,
        })

        if (users.docs && users.docs.length > 0) {
          userId = users.docs[0].id
          console.log('Found default user:', userId)
        }
      } catch (findUserError) {
        console.error('Error finding a default user:', findUserError)
      }
    }

    // Add the owner field if we found a user
    if (userId) {
      data.owner = userId
    } else {
      // If we still don't have a user, create a mock one for testing
      console.log('No user found, creating a mock business without owner')
    }

    console.log('Creating business with data:', JSON.stringify(data, null, 2))

    // Create a new business
    try {
      const business = await payload.create({
        collection: 'businesses',
        data: data,
      })

      console.log('Business created successfully:', business.id)

      return NextResponse.json({
        id: business.id,
        doc: business,
        message: 'Business created successfully',
      })
    } catch (createError: any) {
      console.error('Error creating business:', createError)

      // If there's a validation error, return a more helpful message
      if (createError.name === 'ValidationError' && createError.data) {
        const invalidFields = Object.keys(createError.data).join(', ')
        return NextResponse.json(
          {
            error: `Validation error: ${invalidFields}`,
            details: createError.data,
          },
          { status: 400 },
        )
      }

      throw createError
    }
  } catch (error: any) {
    console.error('Error creating business:', error)

    // Create a mock business response for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Returning mock business response for development')

      return NextResponse.json({
        id: `mock-${Date.now()}`,
        doc: {
          id: `mock-${Date.now()}`,
          businessName: 'Mock Business (Error Fallback)',
          industry: 'technology',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        message: 'Mock business created (due to error in real creation)',
      })
    }

    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check content type to determine how to parse the request
    const contentType = request.headers.get('content-type') || ''

    let data
    let id

    if (contentType.includes('multipart/form-data')) {
      // Handle form data
      const formData = await request.formData()

      // Check if this is Payload CMS admin interface data
      const payloadData = formData.get('_payload')
      if (payloadData) {
        try {
          // Parse the JSON data from Payload CMS admin interface
          data = JSON.parse(payloadData as string)
          id = data.id
          console.log('Parsed Payload CMS update data:', JSON.stringify(data, null, 2))
        } catch (parseError) {
          console.error('Error parsing _payload field:', parseError)
          return NextResponse.json({ error: 'Invalid JSON in _payload field' }, { status: 400 })
        }
      } else {
        // Get the ID from form data
        id = formData.get('id') as string

        // Convert form data to object
        data = {}
        for (const [key, value] of formData.entries()) {
          if (key === 'id') continue // Skip ID as we already extracted it

          // Handle nested objects with dot notation (e.g., location.city)
          if (key.includes('.')) {
            const [parent, child] = key.split('.')
            if (!data[parent]) data[parent] = {}
            data[parent][child] = value
          } else {
            data[key] = value
          }
        }
      }
    } else {
      // Handle JSON data
      try {
        data = await request.json()
        id = data.id
      } catch (jsonError) {
        console.error('Error parsing JSON:', jsonError)
        return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 })
      }
    }

    if (!id) {
      return NextResponse.json({ error: 'Business ID is required' }, { status: 400 })
    }

    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    // Update the business
    const business = await payload.update({
      collection: 'businesses',
      id,
      data: data,
    })

    return NextResponse.json({
      id: business.id,
      doc: business,
      message: 'Business updated successfully',
    })
  } catch (error: any) {
    console.error('Error updating business:', error)
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 })
  }
}
